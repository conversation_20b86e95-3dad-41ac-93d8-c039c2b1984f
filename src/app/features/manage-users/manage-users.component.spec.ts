// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { ManageUsersComponent } from './manage-users.component';
// import { ChangeDetectorRef } from '@angular/core';
// import { of, throwError } from 'rxjs';
// import { NotificationService } from '../../core/services/notification.service';
// import { RegisterService } from '../../core/services/register.service';
// import { NzModalService } from 'ng-zorro-antd/modal';
// import { COMMON_STRINGS } from '../../core/constants/common';
// import { ApiResponsePageUserResponseDTO } from '../../api-client';
// import { HttpClientTestingModule } from '@angular/common/http/testing';
// import { NoopAnimationsModule } from '@angular/platform-browser/animations';
// import { IUserFields } from 'src/app/core/interface/user-fields';

// describe('ManageUsersComponent', () => {
//   let component: ManageUsersComponent;
//   let fixture: ComponentFixture<ManageUsersComponent>;
//   let registerServiceSpy: jasmine.SpyObj<RegisterService>;
//   let notificationSpy: jasmine.SpyObj<NotificationService>;
//   let modalSpy: jasmine.SpyObj<NzModalService>;

//   beforeEach(async () => {
//     registerServiceSpy = jasmine.createSpyObj('RegisterService', [
//       'getAllUsers',
//       'activeInactiveUser',
//     ]);
//     notificationSpy = jasmine.createSpyObj('NotificationService', [
//       'error',
//       'warning',
//       'success',
//     ]);
//     modalSpy = jasmine.createSpyObj('NzModalService', ['confirm']);

//     await TestBed.configureTestingModule({
//       imports: [
//         HttpClientTestingModule,
//         ManageUsersComponent,
//         NoopAnimationsModule,
//       ],
//       providers: [
//         { provide: RegisterService, useValue: registerServiceSpy },
//         { provide: NotificationService, useValue: notificationSpy },
//         { provide: NzModalService, useValue: modalSpy },
//         ChangeDetectorRef,
//       ],
//     }).compileComponents();

//     fixture = TestBed.createComponent(ManageUsersComponent);
//     component = fixture.componentInstance;
//     component.companyId = 123;
//   });

//   it('should create the component', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should show error if companyId is not provided on init', () => {
//     component.companyId = undefined;
//     registerServiceSpy.getAllUsers.calls.reset();
//     component.ngOnInit();
//     expect(notificationSpy.error).toHaveBeenCalledWith(
//       COMMON_STRINGS.warningMessages.companyIdNotAvailable,
//     );
//   });

//   it('should fetch users and separate active/inactive users', () => {
//     const mockResponse: ApiResponsePageUserResponseDTO = {
//       data: {
//         content: [
//           {
//             id: 1,
//             firstName: 'John',
//             lastName: 'Doe',
//             email: '<EMAIL>',
//             isActive: true,
//             companyId: 123,
//             roleDisplayText: 'Admin',
//             contactNumber: '1234567890',
//             roleId: 1,
//             profilePictureUrl: '',
//             primaryAddress: undefined,
//             billingAddress: undefined,
//           },
//           {
//             id: 2,
//             firstName: 'Jane',
//             lastName: 'Smith',
//             email: '<EMAIL>',
//             isActive: false,
//             companyId: 123,
//             roleDisplayText: 'User',
//             contactNumber: '0987654321',
//             roleId: 2,
//             profilePictureUrl: '',
//             primaryAddress: undefined,
//             billingAddress: undefined,
//           },
//         ],
//         totalPages: 1,
//         totalElements: 2,
//       },
//       success: true,
//     };

//     registerServiceSpy.getAllUsers.and.returnValue(of(mockResponse));
//     component.fetchUsers();

//     expect(registerServiceSpy.getAllUsers).toHaveBeenCalledWith(123);
//     expect(component.manageActiveUsersTableData.length).toBe(1);
//     expect(component.manageInactiveUsersTableData.length).toBe(1);
//     expect(component.manageActiveUsersTableData[0].firstName).toBe('John');
//     expect(component.manageInactiveUsersTableData[0].firstName).toBe('Jane');
//   });

//   it('should show error on fetch users failure', () => {
//     registerServiceSpy.getAllUsers.and.returnValue(
//       throwError(() => new Error('Fetch failed')),
//     );
//     component.fetchUsers();
//     expect(notificationSpy.error).toHaveBeenCalled();
//   });

//   it('should open add user drawer', () => {
//     component.isAddUserFormVisible = false;
//     component.addUserFormVisible();
//     expect(component.isAddUserFormVisible).toBeTrue();
//   });

//   it('should toggle deleted users accordion', () => {
//     component.isManageInactiveUsersAccordionOpen = false;
//     component.toggleDeletedUsersAccordion();
//     expect(component.isManageInactiveUsersAccordionOpen).toBeTrue();
//   });

//   it('should close drawer on onDrawerClosed()', () => {
//     component.isAddUserFormVisible = true;
//     const mockUser: IUserFields = {
//       id: 1,
//       firstName: '',
//       lastName: '',
//       email: '',
//       isActive: true,
//       companyId: 123,
//       roleDisplayText: '',
//       contactNumber: '',
//       roleId: 1,
//       profilePictureUrl: '',
//       primaryAddress: undefined,
//       billingAddress: undefined,
//     };
//     component.userToEdit = mockUser;

//     component.onDrawerClosed();

//     expect(component.isAddUserFormVisible).toBeFalse();
//     expect(component.userToEdit).toBeUndefined();
//   });
// });
