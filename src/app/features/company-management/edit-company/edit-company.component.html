<nz-drawer
  [nzClosable]="false"
  [nzVisible]="isDrawerVisible"
  nzPlacement="right"
  [nzTitle]="drawerTitle"
  [nzExtra]="extra"
  (nzOnClose)="editDrawerClose()"
>
  <ng-container *nzDrawerContent>
    <form
      nz-form
      [formGroup]="editCompanyForm"
      nzLayout="vertical"
      class="company-edit-form"
      data-testid="edit-company-form"
    >
      <div nz-row nzGutter="16">
        <div nz-col nzMd="12">
          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label">Company Name</nz-form-label>
            <nz-form-control>
              <input
                nz-input
                placeholder="Enter Company Name"
                formControlName="name"
                class="form-control form-field edit-company-field"
                data-testid="edit-company-name-input"
              />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label">Address Line 1</nz-form-label>
            <nz-form-control>
              <input
                nz-input
                placeholder="Enter Address Line 1"
                formControlName="addressLine1"
                class="form-control form-field edit-company-field"
                data-testid="edit-primary-address-input"
              />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label">Address Line 2</nz-form-label>
            <nz-form-control>
              <input
                nz-input
                placeholder="Enter Address Line 2"
                formControlName="addressLine2"
                class="form-control form-field edit-company-field"
                data-testid="edit-address-line2-input"
              />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label">Suburb</nz-form-label>
            <nz-form-control>
              <input
                nz-input
                placeholder="Enter Suburb"
                formControlName="suburb"
                class="form-control form-field edit-company-field"
                data-testid="edit-suburb-input"
              />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label">State</nz-form-label>
            <nz-form-control>
              <nz-select
                formControlName="state"
                class="form-control form-field edit-company-field"
                placeholder="Enter State"
                data-testid="edit-state-input"
                [nzLoading]="isLoading"
              >
                <nz-option
                  *ngFor="let state of states$ | async"
                  [nzValue]="state.id"
                  [nzLabel]="state.stateName || ''"
                ></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label">Zipcode</nz-form-label>
            <nz-form-control>
              <nz-select
                formControlName="postalCode"
                nzShowSearch
                class="form-control form-field edit-company-field"
                placeholder="Enter Zipcode"
                data-testid="edit-postalCode-input"
                [nzLoading]="isLoading"
              >
                <nz-option
                  *ngFor="let postalCode of postalCode$ | async"
                  [nzValue]="postalCode.id"
                  [nzLabel]="postalCode.zipCode || ''"
                ></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label"
              >Account Holder Name</nz-form-label
            >
            <nz-form-control>
              <input
                nz-input
                placeholder="Enter Account Holder"
                formControlName="accountsContactName"
                class="form-control form-field edit-company-field"
                data-testid="edit-account-holder-input"
              />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item class="edit-company-form-item">
            <nz-form-label class="form-label edit-company-label"
              >Account Holder Email ID</nz-form-label
            >
            <nz-form-control>
              <input
                nz-input
                placeholder="Enter Account Holder Email ID"
                formControlName="billingEmail"
                class="form-control form-field edit-company-field"
                data-testid="edit-billing-email-input"
              />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
      <div class="company-edit-buttons mt-6 d-flex">
        <button
          nz-button
          nzType="primary"
          class="save-edit-button"
          type="button"
          data-testid="save-edit-button"
          (click)="saveCompanyDetails()"
        >
          Save Company
        </button>
        <button nz-button nzType="primary" nzGhost (click)="editDrawerClose()">
          Cancel
        </button>
      </div>
    </form>
  </ng-container>
</nz-drawer>
<ng-template #drawerTitle>
  <h2>Edit Company</h2>
</ng-template>
<ng-template #extra>
  <button nz-button nzType="primary" nzGhost (click)="editDrawerClose()">
    <nz-icon nzType="close-circle" nzTheme="fill" />
    Close
  </button>
</ng-template>
