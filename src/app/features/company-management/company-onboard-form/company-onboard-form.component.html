<div class="company-form-wrapper" [ngClass]="{ 'billing-form': isBillingStep }">
  <form
    nz-form
    [formGroup]="currentForm"
    nzLayout="vertical"
    class="company-form"
    [attr.data-test-id]="isBillingStep ? 'billing-form' : 'company-form'"
  >
    <div *ngIf="isBillingStep" class="mb-3 billing-checkbox">
      <nz-form-item>
        <nz-form-control>
          <label
            nz-checkbox
            formControlName="sameAsCompanyDetails"
            (ngModelChange)="onSameAsCompanyDetailsChange()"
            class="checkbox-label"
            id="sameAsCompanyDetails"
            data-testid="same-as-company-details-checkbox"
          >
            Same as Company Details
          </label>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div nz-row nzGutter="16">
      <!-- LEFT COLUMN -->
      <div nz-col nzMd="12" class="left-column">
        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Company Name</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'Company Name is required'">
            <input
              nz-input
              placeholder="Enter Company Name"
              formControlName="name"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep
                  ? 'billing-company-name-input'
                  : 'company-name-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >ABN</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'ABN is required'">
            <input
              nz-input
              placeholder="Enter ABN"
              formControlName="abn"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep ? 'billing-abn-input' : 'abn-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >ACN</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'ACN is required'">
            <input
              nz-input
              placeholder="Enter ACN"
              formControlName="acn"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep ? 'billing-acn-input' : 'acn-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Accounts Email</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'Valid email is required'">
            <input
              nz-input
              placeholder="Enter email"
              type="email"
              formControlName="billingEmail"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep ? 'billing-email-input' : 'accounts-email-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Key Contact Person</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'Contact name is required'">
            <input
              nz-input
              placeholder="Enter accounts contact"
              formControlName="accountsContactName"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep
                  ? 'billing-contact-input'
                  : 'accounts-contact-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label class="form-label">Accounts Telephone</nz-form-label>
          <nz-form-control>
            <input
              nz-input
              placeholder="Enter telephone"
              type="tel"
              formControlName="accountsContactNumber"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep
                  ? 'billing-telephone-input'
                  : 'accounts-telephone-input'
              "
            />
          </nz-form-control>
        </nz-form-item>
      </div>

      <!-- RIGHT COLUMN -->
      <div nz-col nzMd="12" class="right-column">
        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Address Line 1</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'Address Line 1 is required'">
            <input
              nz-input
              placeholder="Enter address"
              formControlName="addressLine1"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep
                  ? 'billing-address-line1-input'
                  : 'address-line1-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label class="form-label">Address Line 2</nz-form-label>
          <nz-form-control>
            <input
              nz-input
              placeholder="Enter address"
              formControlName="addressLine2"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep
                  ? 'billing-address-line2-input'
                  : 'address-line2-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label class="form-label">Suburb</nz-form-label>
          <nz-form-control>
            <input
              nz-input
              placeholder="Enter suburb"
              formControlName="suburb"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep ? 'billing-suburb-input' : 'suburb-input'
              "
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >State</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'State is required'">
            <nz-select
              [nzPlaceHolder]="'Enter state'"
              formControlName="state"
              class="form-control form-field"
              [disabled]="isBillingStep && sameAsCompanyDetails"
              [attr.data-testid]="
                isBillingStep ? 'billing-state-input' : 'state-input'
              "
              [nzLoading]="isLoading"
            >
              <nz-option
                *ngFor="let state of states$ | async"
                [nzValue]="state.id"
                [nzLabel]="state.stateName || ''"
              ></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="mb-4">
          <nz-form-label [nzRequired]="true" class="form-label"
            >Zip code</nz-form-label
          >
          <nz-form-control [nzErrorTip]="'Zip code is required'">
            <nz-select
              [nzPlaceHolder]="'Enter zip code'"
              formControlName="postalCode"
              class="form-control form-field"
              [disabled]="
                (isBillingStep && sameAsCompanyDetails) ||
                !currentForm.get('state')?.value
              "
              [attr.data-testid]="
                isBillingStep ? 'billing-postalCode-input' : 'postalCode-input'
              "
              [nzLoading]="isLoading"
            >
              <nz-option
                *ngFor="let postalCode of postalCode$ | async"
                [nzValue]="postalCode.id"
                [nzLabel]="postalCode.zipCode || ''"
              ></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>
  </form>
</div>
