import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { FormGroup, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Observable, of, Subscription } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NotificationService } from '../../../core/services/notification.service';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { StateDTO, ZipCodeDTO } from '../../../api-client';
import {
  OnboardingState,
  ICompanyFormData,
} from '../../../core/interface/company-fields';
import { CompanyForm } from '../../../core/components/company-form';
import { CompanyState } from '../../../core/components/company-state';
import { CompanyValidation } from '../../../core/components/company-validation';

@Component({
  selector: 'app-company-onboard-form',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    NzFormModule,
    NzInputModule,
    NzButtonModule,
    NzGridModule,
    NzCheckboxModule,
    NzSelectModule,
  ],
  templateUrl: './company-onboard-form.component.html',
  styleUrl: './company-onboard-form.component.scss',
})
export class CompanyOnboardFormComponent implements OnInit, OnDestroy {
  @Input() isCompanyStep = false;
  @Input() isBillingStep = false;
  @Output() sameAsCompanyDetailsChange = new EventEmitter<boolean>();
  @Output() formValidityChange = new EventEmitter<boolean>();

  companyForm!: FormGroup;
  billingForm!: FormGroup;
  sameAsCompanyDetails = false;
  isLoading = false;
  states$: Observable<StateDTO[]> = of([]);
  postalCode$: Observable<ZipCodeDTO[]> = of([]);
  private subscriptions = new Subscription();

  get currentForm(): FormGroup {
    return this.isBillingStep ? this.billingForm : this.companyForm;
  }

  constructor(
    private notification: NotificationService,
    private onboardingStateService: CompanyState,
    private companyFormService: CompanyForm,
    private companyValidationService: CompanyValidation,
  ) {
    this.companyForm = this.companyFormService.createCompanyForm();
    this.billingForm = this.companyFormService.createBillingForm();
  }

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    this.loadStateFromService();
    this.fetchStates();
    this.setupAllSubscriptions();
    setTimeout(() => this.initializeBillingDetails(), 100);
  }

  private setupAllSubscriptions(): void {
    this.setupFormSubscriptions();
    this.setupCompanyFormWatcher();
    this.setupFormSaving();
  }

  private initializeBillingDetails(): void {
    const currentState =
      this.onboardingStateService.getCurrentOnboardingState();
    if (!currentState.billingDetails) {
      this.createDefaultBillingDetails();
    }
    if (this.isBillingStep) {
      this.handleBillingStepInitialization();
    }
  }

  private createDefaultBillingDetails(): void {
    const defaultBillingDetails =
      this.companyFormService.getDefaultBillingDetailsObject();
    this.onboardingStateService.updateBillingDetails(defaultBillingDetails);
    this.sameAsCompanyDetails = true;
    this.billingForm
      ?.get('sameAsCompanyDetails')
      ?.setValue(true, { emitEvent: false });
  }

  private handleBillingStepInitialization(): void {
    this.saveBillingFormToState();
    if (this.sameAsCompanyDetails) {
      this.copyCompanyDetailsToBilling();
    }
  }

  private saveBillingFormToState(): void {
    if (!this.billingForm) return;
    const billingValue = {
      ...this.billingForm.value,
      sameAsCompanyDetails: this.sameAsCompanyDetails,
    };
    this.onboardingStateService.updateBillingDetails(billingValue);
  }

  private setupFormSaving(): void {
    this.setupCompanyFormSaving();
    this.setupBillingFormSaving();
  }

  private setupCompanyFormSaving(): void {
    if (!this.companyForm) return;
    this.subscriptions.add(
      this.companyForm.valueChanges.subscribe((value) => {
        this.onboardingStateService.updateCompanyDetails(value);
        if (this.sameAsCompanyDetails && this.isBillingStep) {
          this.copyCompanyDetailsToBilling();
        }
      }),
    );
  }

  private setupBillingFormSaving(): void {
    if (!this.billingForm) return;
    this.subscriptions.add(
      this.billingForm.valueChanges.subscribe((value) => {
        const billingData = {
          ...value,
          sameAsCompanyDetails: this.sameAsCompanyDetails,
        };
        this.onboardingStateService.updateBillingDetails(billingData);
      }),
    );
  }

  private loadStateFromService(): void {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    this.loadCompanyState(state);
    this.loadBillingState(state);
  }

  private loadCompanyState(state: OnboardingState): void {
    if (state.companyDetails && this.companyForm) {
      this.companyForm.patchValue(state.companyDetails, { emitEvent: false });
    }
  }

  private loadBillingState(state: OnboardingState): void {
    if (!this.billingForm) return;
    if (state.billingDetails) {
      this.billingForm.patchValue(state.billingDetails, { emitEvent: false });
      this.sameAsCompanyDetails =
        state.billingDetails.sameAsCompanyDetails || false;
    } else if (this.isBillingStep) {
      this.billingForm.patchValue(
        { sameAsCompanyDetails: false },
        { emitEvent: false },
      );
      this.sameAsCompanyDetails = false;
    }
    this.sameAsCompanyDetails =
      this.billingForm.get('sameAsCompanyDetails')?.value || false;
  }

  private setupCompanyFormWatcher(): void {
    if (this.companyForm) {
      this.subscriptions.add(
        this.companyForm.valueChanges.subscribe(() => {
          if (this.sameAsCompanyDetails && this.billingForm) {
            this.copyCompanyDetailsToBilling();
          }
        }),
      );
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  private setupFormSubscriptions(): void {
    this.setupCompanyStateSubscription();
    this.setupBillingStateSubscription();
    this.setupSameAsCompanyDetailsSubscription();
  }

  private setupCompanyStateSubscription(): void {
    if (!this.companyForm) return;
    this.subscriptions.add(
      this.companyForm.get('state')?.valueChanges.subscribe((stateId) => {
        this.handleCompanyStateChange(stateId);
      }),
    );
  }

  private setupBillingStateSubscription(): void {
    if (!this.billingForm) return;
    this.subscriptions.add(
      this.billingForm.get('state')?.valueChanges.subscribe((stateId) => {
        this.handleBillingStateChange(stateId);
      }),
    );
  }

  private setupSameAsCompanyDetailsSubscription(): void {
    if (!this.billingForm) return;
    this.subscriptions.add(
      this.billingForm
        .get('sameAsCompanyDetails')
        ?.valueChanges.subscribe((value) => {
          this.handleSameAsCompanyDetailsChange(value);
        }),
    );
  }

  private handleCompanyStateChange(stateId: number): void {
    if (stateId && !this.isBillingStep) {
      this.fetchZipcodes(stateId);
      this.companyForm.get('postalCode')?.enable({ emitEvent: false });
    } else if (!stateId && !this.isBillingStep) {
      this.resetPostalCodes();
      this.companyForm.get('postalCode')?.disable({ emitEvent: false });
    }
  }

  private handleBillingStateChange(stateId: number): void {
    if (stateId && this.isBillingStep && !this.sameAsCompanyDetails) {
      this.fetchZipcodes(stateId);
      this.billingForm.get('postalCode')?.enable({ emitEvent: false });
    } else if ((!stateId || this.sameAsCompanyDetails) && this.isBillingStep) {
      this.resetPostalCodes();
      this.billingForm.get('postalCode')?.disable({ emitEvent: false });
    }
  }

  private handleSameAsCompanyDetailsChange(value: boolean): void {
    if (value !== this.sameAsCompanyDetails) {
      this.sameAsCompanyDetails = value;
      this.sameAsCompanyDetailsChange.emit(this.sameAsCompanyDetails);
      if (this.sameAsCompanyDetails) {
        this.refreshCompanyDataFromState();
        this.copyCompanyDetailsToBilling();
      } else {
        this.clearBillingForm();
      }
      setTimeout(() => this.saveBillingFormToState(), 50);
    }
  }

  private resetPostalCodes(): void {
    this.postalCode$ = of([]);
    this.currentForm.get('postalCode')?.reset();
  }

  onSameAsCompanyDetailsChange(): void {
    const checkboxValue = this.billingForm.get('sameAsCompanyDetails')?.value;
    this.sameAsCompanyDetails = checkboxValue;
    this.sameAsCompanyDetailsChange.emit(this.sameAsCompanyDetails);
    if (this.sameAsCompanyDetails) {
      this.handleSameAsChecked();
    } else {
      this.clearBillingForm();
    }
    setTimeout(() => this.saveBillingFormToState(), 50);
  }

  private handleSameAsChecked(): void {
    this.refreshCompanyDataFromState();
    this.copyCompanyDetailsToBilling();
  }

  private refreshCompanyDataFromState(): void {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    if (state.companyDetails && this.companyForm) {
      this.companyForm.patchValue(state.companyDetails, { emitEvent: false });
    }
  }

  private copyCompanyDetailsToBilling(): void {
    if (!this.billingForm) return;
    const dataSource = this.getCompanyDataSource();
    if (!dataSource) return;
    this.patchBillingFormWithCompanyData(dataSource);
    this.handleStateBasedZipcodes(dataSource);
    this.updateBillingFormValidators();
    this.saveBillingFormToState();
  }

  private getCompanyDataSource(): ICompanyFormData | null {
    const state = this.onboardingStateService.getCurrentOnboardingState();
    return state.companyDetails || this.companyForm?.value;
  }

  private patchBillingFormWithCompanyData(dataSource: ICompanyFormData): void {
    const billingData = {
      name: dataSource.name || '',
      abn: dataSource.abn || '',
      acn: dataSource.acn || '',
      billingEmail: dataSource.billingEmail || '',
      accountsContactName: dataSource.accountsContactName || '',
      accountsContactNumber: dataSource.accountsContactNumber || '',
      addressLine1: dataSource.addressLine1 || '',
      addressLine2: dataSource.addressLine2 || '',
      suburb: dataSource.suburb || '',
      state: dataSource.state || '',
      postalCode: dataSource.postalCode || '',
    };
    this.billingForm.patchValue(billingData, { emitEvent: false });
  }

  private handleStateBasedZipcodes(dataSource: ICompanyFormData): void {
    if (dataSource.state) {
      const stateId =
        typeof dataSource.state === 'string'
          ? parseInt(dataSource.state, 10)
          : dataSource.state;
      this.fetchZipcodes(stateId);
    }
  }

  private updateBillingFormValidators(): void {
    if (!this.billingForm) return;
    this.companyValidationService.updateBillingFormValidators(
      this.billingForm,
      this.sameAsCompanyDetails,
    );
  }

  private clearBillingForm(): void {
    if (!this.billingForm) return;
    this.companyFormService.clearBillingForm(this.billingForm);
    this.postalCode$ = of([]);
  }

  fetchStates(): void {
    this.isLoading = true;
    this.states$ = this.companyFormService.fetchStates().pipe(
      map((states: StateDTO[]) => {
        this.isLoading = false;
        return states;
      }),
      catchError(() => {
        this.isLoading = false;
        return of([]);
      }),
    );
  }

  fetchZipcodes(stateId: number): void {
    if (!stateId) {
      this.resetAndDisablePostalCodes(
        COMMON_STRINGS.warningMessages.selectValidState,
      );
      return;
    }

    this.isLoading = true;
    this.postalCode$ = this.companyFormService.fetchZipcodes(stateId).pipe(
      map((zipcodes: ZipCodeDTO[]) => {
        this.isLoading = false;
        this.handleZipcodesResponse(zipcodes);
        return zipcodes;
      }),
      catchError(() => {
        this.isLoading = false;
        this.resetAndDisablePostalCodes(
          COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
        );
        return of([]);
      }),
    );
  }

  private handleZipcodesResponse(zipcodes: ZipCodeDTO[]): void {
    if (zipcodes.length === 0) {
      this.resetAndDisablePostalCodes(
        COMMON_STRINGS.errorMessages.failedToFetchZipcodes,
      );
    } else {
      this.enablePostalCodes();
    }
  }

  private resetAndDisablePostalCodes(warningMessage: string): void {
    this.postalCode$ = of([]);
    this.notification.warning(warningMessage);
    this.currentForm?.get('postalCode')?.reset('', { emitEvent: false });
    this.currentForm?.get('postalCode')?.disable({ emitEvent: false });
  }

  private enablePostalCodes(): void {
    this.currentForm?.get('postalCode')?.enable({ emitEvent: false });
  }
}
