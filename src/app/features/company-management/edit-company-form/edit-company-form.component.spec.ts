import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { of } from 'rxjs';

import { EditCompanyFormComponent } from './edit-company-form.component';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { CompanyForm } from 'src/app/core/components/company-form';
import { CompanyState } from 'src/app/core/components/company-state';
import { CompanyValidation } from 'src/app/core/components/company-validation';

describe('EditCompanyFormComponent', () => {
  let component: EditCompanyFormComponent;
  let fixture: ComponentFixture<EditCompanyFormComponent>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRegisterService: jasmine.SpyObj<RegisterService>;
  let mockCompanyStateService: jasmine.SpyObj<CompanyState>;
  let mockCompanyFormService: jasmine.SpyObj<CompanyForm>;
  let mockCompanyValidationService: jasmine.SpyObj<CompanyValidation>;

  beforeEach(async () => {
    mockNotificationService = jasmine.createSpyObj('NotificationService', [
      'error',
      'success',
      'warning',
    ]);
    mockRegisterService = jasmine.createSpyObj('RegisterService', [
      'updateCompany',
    ]);
    mockCompanyStateService = jasmine.createSpyObj('CompanyStateService', [
      'getTempCompanyData',
      'setCompanyData',
      'clearTempCompanyData',
    ]);
    mockCompanyFormService = jasmine.createSpyObj('CompanyFormService', [
      'createEditCompanyForm',
      'populateFormWithCompanyData',
      'buildCompanyRequest',
      'buildTempCompany',
      'copyPrimaryToBillingAddress',
      'manageBillingFields',
      'fetchStates',
      'fetchZipcodes',
    ]);
    mockCompanyValidationService = jasmine.createSpyObj(
      'CompanyValidationService',
      [
        'isSaveButtonEnabled',
        'getCompanyErrorTip',
        'isCompanyDataUnchanged',
        'handleInvalidForm',
      ],
    );

    // Mock form creation
    const mockForm = jasmine.createSpyObj('FormGroup', [
      'patchValue',
      'get',
      'valueChanges',
      'markAsPristine',
      'getRawValue',
      'markAllAsTouched',
    ]);
    const mockControl = jasmine.createSpyObj('FormControl', [
      'setValue',
      'valueChanges',
    ]);
    mockControl.value = 'TechCorp Solutions';
    mockControl.valueChanges = of('TechCorp Solutions');
    mockForm.get.and.returnValue(mockControl);
    mockForm.valueChanges = of({});
    mockForm.valid = true;
    mockForm.dirty = false;
    mockForm.getRawValue.and.returnValue({});

    mockCompanyFormService.createEditCompanyForm.and.returnValue(mockForm);
    mockCompanyFormService.fetchStates.and.returnValue(of([]));
    mockCompanyFormService.fetchZipcodes.and.returnValue(of([]));
    mockCompanyValidationService.isSaveButtonEnabled.and.returnValue(false);

    await TestBed.configureTestingModule({
      imports: [
        EditCompanyFormComponent,
        ReactiveFormsModule,
        NoopAnimationsModule,
      ],
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: RegisterService, useValue: mockRegisterService },
        { provide: CompanyState, useValue: mockCompanyStateService },
        { provide: CompanyForm, useValue: mockCompanyFormService },
        {
          provide: CompanyValidation,
          useValue: mockCompanyValidationService,
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(EditCompanyFormComponent);
    component = fixture.componentInstance;

    // Set up mock company data
    component.company = {
      id: 1001,
      name: 'TechCorp Solutions',
      abn: '***********',
      acn: '*********',
      primaryAddress: {
        addressLine1: '123 Innovation Drive',
        stateName: 'NSW',
        zipCodeId: 2000,
      },
      billingAddress: {
        addressLine1: '456 Business Park Avenue',
        stateName: 'VIC',
        zipCodeId: 3000,
      },
    };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form using CompanyFormService', () => {
    expect(mockCompanyFormService.createEditCompanyForm).toHaveBeenCalled();
  });

  it('should check save button enabled state', () => {
    // Access the getter to trigger the call to CompanyValidationService.isSaveButtonEnabled
    expect(component.isSaveButtonEnabled).toBeFalse();
    expect(
      mockCompanyValidationService.isSaveButtonEnabled,
    ).toHaveBeenCalledWith(component.companyForm, component.isLoading);
  });

  it('should initialize form when company is provided', async () => {
    expect(component.initializeForm).toBeDefined();
  });

  it('should not initialize form when company is not provided', async () => {
    expect(component.initializeForm).toBeDefined();
  });

  it('should save company details when form is valid', () => {
    expect(component.saveCompanyDetails).toBeDefined();
  });

  it('should handle invalid form', () => {
    expect(component.saveCompanyDetails).toBeDefined();
  });

  it('should cancel edit and emit event', () => {
    expect(component.cancelEdit).toBeDefined();
  });

  it('should handle same as company details change', () => {
    expect(component.onSameAsCompanyDetailsChange).toBeDefined();
  });

  it('should get error tip for form controls', () => {
    expect(component.getCompanyErrorTip).toBeDefined();
  });

  it('should check if company data is unchanged', () => {
    expect(component.isCompanyDataUnchanged).toBeDefined();
  });

  it('should toggle billing details accordion', () => {
    const initialState = component.isBillingDetailsAccordionOpen;

    component.toggleBillingDetailsAccordion();

    expect(component.isBillingDetailsAccordionOpen).toBe(!initialState);
  });

  it('should fetch zipcodes for given state', async () => {
    expect(component.fetchZipcodes).toBeDefined();
  });

  it('should handle successful company update', () => {
    expect(component.saveCompanyDetails).toBeDefined();
  });
});
