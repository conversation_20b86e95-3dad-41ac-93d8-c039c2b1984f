.company-edit-form-buttons {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.save-edit-button {
  height: 36px;
  width: 170px;
  background-color: var(--primary-button-color);
  border-radius: 7px;
  color: #ffffff;
}

.edit-company-form-field {
  height: 45px;
  border: 1px solid #d8cece;
  border-radius: 7px;
  width: 27rem;
}

.left-column {
  padding-left: 20px;
  padding-right: 50px;
}

.right-column {
  padding-left: 50px;
  padding-right: 20px;
}

::ng-deep .edit-drawer .ant-form-vertical .ant-form-item-label {
  font-weight: bold;
}

::ng-deep .ant-drawer-title {
  font-size: 22px !important;
  font-weight: bold;
}

::ng-deep .ant-form-item {
  margin-bottom: 0;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
  color: #ff4d4f;
}

input.is-invalid {
  background-image: none !important;
}

.nz-form-field {
  height: 45px;
}

:host ::ng-deep .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .ant-form-item-explain-error {
  color: #ff4d4f;
}

.left-company-edit-form-fields {
  display: block;
  padding-right: 7rem;
}

.right-company-edit-form-fields {
  display: block;
  padding-left: 7rem;
}

.edit-form-checkbox{
  margin-bottom: 20px;
  margin-left: 10px;
}

.form-buttons {
  gap: 15px;
  display: flex;
}

.save-editCompany-button,
.cancel-editCompany-button {
  height: 40px;
  border-radius: 7px;
  padding: 0 20px;
}

.save-editCompany-button {
  background-color: var(--primary-button-color);
  color: #ffffff;
  border: none;
}

.cancel-editCompany-button {
  background-color: #6c757d;
  color: #ffffff;
  border: none;
}

.save-editCompany-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.cancel-editCompany-button:hover {
  background-color: #5a6268;
}

.save-editCompany-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  cursor: not-allowed;
}
