.company-view-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.tabset {
  margin-top: 35px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  justify-content: space-between !important;
  padding-left: 50px;
  padding-right: 50px;
}

::ng-deep .ant-tabs-content-holder {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

::ng-deep .ant-tabs-tabpane {
  flex: 1;
  overflow-y: auto;
  overflow-x: auto;
}

::ng-deep .company-view-tabs .ant-tabs-tab {
  font-size: 18px;
  margin-right: 0 !important;
}

.company-details-container {
  padding-top: 15px;
}

.action-buttons {
  gap: 20px;
}

.field-label {
  color: grey;
}

.inactive-accordian {
  cursor: pointer;
  border-radius: 4px;
  color: var(--primary-button-color);  
  font-size: 16px;
  font-weight: bold;
}

.active-accordian {
  cursor: pointer;
  border-radius: 4px;
  color: var(--primary-button-color);
  justify-content: space-between;
  font-size: 16px;
  font-weight: bold;
}

.user-edit-form-buttons {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.save-edit-button {
  height: 36px;
  width: 120px;
  background-color: var(--primary-button-color);
  border-radius: 7px;
  color: #ffffff;
}

.cancel-edit-button {
  height: 36px;
  width: 120px;
  background-color: var(--primary-button-color);
  border-radius: 7px;
  color: #ffffff;
}

::ng-deep .ant-drawer-title {
  font-size: 22px !important;
  font-weight: bold;
}

.save-edit-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  border-color: #d3d3d3;
  cursor: not-allowed;
}

.company-edit-form-fields {
  display: block;
}

.input-company-field {
  height: 45px;
  border-radius: 3px;
  border: 1px solid #d8cece;
}

.company-details-container .form-buttons {
  gap: 15px;
  display: flex;
}

.save-editCompany-button,
.cancel-editCompany-button {
  height: 40px;
}

nz-breadcrumb {
  padding-left: 4rem;
}

.active-breadcrumb {
  color: #1890ff;
  cursor: pointer;
  font-weight: bold;
}

::ng-deep .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  position: relative;
  background-color: #fff;
  border: none;
  border-radius: none;
  transition: none;
}

::ng-deep nz-input-group.ant-input-affix-wrapper,
nz-select.ant-select .ant-select-selector {
  border-radius: none;
  box-shadow: none;
}

.company-edit-form {
  margin-right: 0;
  margin-left: 0;
}

.user-grid {
  width: 100%;
  font-size: 14px;
  color: #333;

  .user-grid-header,
  .user-grid-row {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
    gap: 16px;
    padding: 8px 0;
    align-items: center;
  }

  .user-grid-header {
    font-weight: bold;
    color: #666;
    border-bottom: 1px solid #e8e8e8;
  }

  .user-grid-cell {
    word-break: break-word;
  }

  .user-grid-cell a {
    color: #1890ff;
    cursor: pointer;
    text-decoration: none;
  }

  .user-grid-cell a:hover {
    text-decoration: underline;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: #999;
  }
}

.non-active-breadcrumb {
  cursor: pointer;
  color: #666;
}

.non-active-breadcrumb:hover {
  color: #1890ff;
}

.view-company-action-buttons{
  padding-left: 153px;
}

.edit-toggle-icon{
  font-size: 28px;
  color: var(--primary-button-color);
}
