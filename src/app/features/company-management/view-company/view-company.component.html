<nz-breadcrumb>
  <nz-breadcrumb-item>
    <span class="non-active-breadcrumb" (click)="goBack()"
      >Company Management</span
    >
  </nz-breadcrumb-item>
  <nz-breadcrumb-item>
    <span class="active-breadcrumb">View {{ company?.name }}</span>
  </nz-breadcrumb-item>
</nz-breadcrumb>

<div class="company-view-tabs">
  <nz-tabset class="tabset" [nzTabBarGutter]="32">
    <!-- Company Details -->
    <nz-tab nzTitle="Company Details">
        <div class="container-fluid">
          <div class="company-details-container row">
            
            @if (!isEditFormVisible) {
              <div class="col-md-2">
                <p class="field-label">Company Name:</p>
                <p class="field-label">ABN:</p>
                <p class="field-label">ACN:</p>
                <p class="field-label">Contact:</p>
                <p class="field-label">Email Id:</p>
                <p class="field-label">Address:</p>
                <p class="field-label">Billing Address:</p>
                <p class="field-label">Key Contact Person:</p>
              </div>
              <div class="col-md-8">
                <p>{{ company?.name || 'N/A' }}</p>
                <p>{{ company?.abn || 'N/A' }}</p>
                <p>{{ company?.acn || 'N/A' }}</p>
                <p>{{ company?.accountsContactName || 'N/A' }}</p>
                <p>{{ company?.billingEmail || 'N/A' }}</p>
                <p>{{ primaryAddressFormatted || 'N/A' }}</p>
                <p>{{ billingAddressFormatted || 'N/A' }}</p>
                <p>{{ company?.accountsContactName || 'N/A' }}</p>
              </div>
              <div class="col-md-2 view-company-action-buttons">
                <div
                  class="action-buttons d-flex align-items-end"
                  style="margin-bottom: 16px"
                >
                  <nz-icon
                    nzType="edit"
                    nzTheme="outline"
                    class="edit-toggle-icon"
                    (click)="editCompany()"
                  />
                  <nz-switch
                    
                    [ngModel]="company?.isActive"
                    [nzCheckedChildren]="'Active'"
                    [nzUnCheckedChildren]="'Inactive'"
                    (click)="toggleActive()"
                  ></nz-switch>
                </div>
              </div>
            } @else {
              <!-- Edit Company Form Component -->
              <app-edit-company-form
                [isVisible]="isEditFormVisible"
                [company]="company"
                (companySaved)="onCompanySaved($event)"
                (formCancelled)="onFormCancelled()"
              >
              </app-edit-company-form>
            }
            <!-- Active/Inactive Users Component -->
            <app-active-inactive-users
              [companyId]="companyId"
              [company]="company"
            >
            </app-active-inactive-users>
          </div>
        </div>
    </nz-tab>

    <!-- Billing Details -->
    <nz-tab nzTitle="Billing Details">
      <ng-template nz-tab>
        <p>Billing details content goes here...</p>
      </ng-template>
    </nz-tab>

    <!-- Pricing Management -->
    <nz-tab nzTitle="Pricing Management">
      <ng-template nz-tab>
        <app-company-pricing-management [companyId]="companyId">
        </app-company-pricing-management>
      </ng-template>
    </nz-tab>
  </nz-tabset>
</div>
