input.is-invalid {
  background-image: none !important;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
  color: #ff4d4f;
}

:host ::ng-deep .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .ant-form-item-explain-error {
  color: #ff4d4f;
}

.image-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.camera-icon {
  position: absolute;
  bottom: 5px;
  right: 5px;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
}

.camera-icon:hover {
  background-color: #0056b3;
}

::ng-deep .drawer-header .ant-drawer-title {
  font-size: 22px !important;
  font-weight: bold;
}

::ng-deep .ant-drawer-body {
  padding: 24px;
}

.user-edit-form-buttons {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-top: 20px;
}

.save-edit-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  border-color: #d3d3d3;
  cursor: not-allowed;
}

.save-edit-button:hover:not(:disabled) {
  background-color: #0056b3;
}

.form-item{
  margin-bottom: 25px;
}

.form-label{
  padding: 0 !important;
}
