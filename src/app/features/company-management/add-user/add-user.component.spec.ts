// import { ComponentFixture, TestBed } from '@angular/core/testing';
// import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
// import { provideAnimations } from '@angular/platform-browser/animations';
// import { provideNzIcons } from 'ng-zorro-antd/icon';
// import { CameraOutline } from '@ant-design/icons-angular/icons';
// import { AddUserComponent } from './add-user.component';
// import { NotificationService } from '../../../core/services/notification.service';
// import { SharedLookup } from '../../../core/components/shared-lookup';
// import { CompanyState } from '../../../core/components/company-state';
// import { UserCreation } from '../../../core/components/user-creation';
// import { RegisterService } from '../../../core/services/register.service';
// import { UserRequestMapper } from '../../../core/components/user-form';
// import { of } from 'rxjs';

// class MockNotificationService {
//   error = jasmine.createSpy('error');
// }
// class MockSharedLookup {
//   fetchRoles = jasmine.createSpy('fetchRoles').and.returnValue(of([]));
// }
// class MockCompanyState {
//   triggerFileInput = jasmine.createSpy('triggerFileInput');
//   fileUploadEvent = jasmine.createSpy('fileUploadEvent');
// }
// class MockUserCreation {
//   createUserWithProfilePicture = jasmine
//     .createSpy('createUserWithProfilePicture')
//     .and.returnValue(of({ success: true, userData: {} }));
//   handleFileSelection = jasmine.createSpy('handleFileSelection');
// }
// class MockRegisterService {
//   updateUser = jasmine
//     .createSpy('updateUser')
//     .and.returnValue(of({ success: true, data: {} }));
// }
// class MockUserRequestMapper {
//   createUserRequest = jasmine
//     .createSpy('createUserRequest')
//     .and.returnValue({});
//   mapUserResponseToUserFields = jasmine
//     .createSpy('mapUserResponseToUserFields')
//     .and.returnValue({});
// }

// describe('AddUserComponent', () => {
//   let component: AddUserComponent;
//   let fixture: ComponentFixture<AddUserComponent>;
//   let notificationService: NotificationService;

//   beforeEach(async () => {
//     await TestBed.configureTestingModule({
//       imports: [ReactiveFormsModule, AddUserComponent],
//       providers: [
//         provideAnimations(),
//         provideNzIcons([CameraOutline]),
//         FormBuilder,
//         { provide: NotificationService, useClass: MockNotificationService },
//         { provide: SharedLookup, useClass: MockSharedLookup },
//         { provide: CompanyState, useClass: MockCompanyState },
//         { provide: UserCreation, useClass: MockUserCreation },
//         { provide: RegisterService, useClass: MockRegisterService },
//         { provide: UserRequestMapper, useClass: MockUserRequestMapper },
//       ],
//     }).compileComponents();

//     fixture = TestBed.createComponent(AddUserComponent);
//     component = fixture.componentInstance;
//     notificationService = TestBed.inject(NotificationService);
//     fixture.detectChanges();
//   });

//   it('should create form with required controls', () => {
//     expect(component.addUserForm.contains('firstName')).toBeTrue();
//     expect(component.addUserForm.contains('lastName')).toBeTrue();
//     expect(component.addUserForm.contains('email')).toBeTrue();
//     expect(component.addUserForm.contains('contactNumber')).toBeTrue();
//     expect(component.addUserForm.contains('roleId')).toBeTrue();
//   });

//   it('should return true for unique email', () => {
//     component.existingUsers = [{ email: '<EMAIL>' }];
//     expect(component.isEmailUnique('<EMAIL>')).toBeTrue();
//   });

//   it('should return false for duplicate email', () => {
//     component.existingUsers = [{ email: '<EMAIL>' }];
//     expect(component.isEmailUnique('<EMAIL>')).toBeFalse();
//   });

//   it('should mark form as invalid if required fields are missing', () => {
//     component.addUserForm.patchValue({
//       firstName: '',
//       lastName: '',
//       email: '',
//       contactNumber: '',
//       roleId: null,
//     });
//     expect(component.addUserForm.valid).toBeFalse();
//   });

//   it('should emit drawerClosed on toggleAddUserDrawer', () => {
//     spyOn(component.drawerClosed, 'emit');
//     component.isVisible = true;
//     component.toggleAddUserDrawer();
//     expect(component.drawerClosed.emit).toHaveBeenCalled();
//   });

//   it('should call notification error if companyId is missing when adding user', () => {
//     component.companyId = undefined;
//     component.addUserForm.patchValue({
//       firstName: '',
//       lastName: '',
//       email: '',
//       contactNumber: '',
//       roleId: null,
//     });
//     component.submitForm();

//     // ✅ Now testing via injected service directly, no need for `any`
//     expect(notificationService.error).toHaveBeenCalled();
//   });
// });
