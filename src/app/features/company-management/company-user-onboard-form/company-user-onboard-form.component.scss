.company-form-wrapper {
  display: flex;
  justify-content: center;
}

.company-form {
  position: relative;
  width: 100%;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background-color: #E8F3FE;
}

.add-button {
  width: 90px;
  height: 40px;
  background-color: var(--primary-button-color);
  z-index: 1000;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  color: #ffffff;
}

.add-another-button {
  width: 150px;
  height: 40px;
  background-color: transparent;
  color: var(--primary-button-color);
  border: 2px solid var(--primary-button-color);
  border-radius: 5px;
  z-index: 1000;
  cursor: pointer;
  transition:
    background-color 0.3s,
    color 0.3s;
}

.add-another-button:hover {
  background-color: var(--primary-button-color);
  color: #ffffff;
}

.add-button:hover,
.add-another-button:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

.add-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  border-color: #d3d3d3;
  cursor: not-allowed;
}

.profile-image {
  padding-left: 30px;
  padding-bottom: 30px;
}

input.is-invalid {
  background-image: none !important;
}

.text-danger {
  font-size: 12px;
  margin-top: 5px;
  color: #ff4d4f;
}

:host ::ng-deep .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f !important;
}

:host ::ng-deep .ant-form-item-explain-error {
  color: #ff4d4f;
}

.image-wrapper {
  position: relative;
  display: inline-block;
}

.camera-icon {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: #ffffff;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
}

.camera-icon:hover {
  background-color: #0056b3;
}

.user-column{
  padding-left: 130px;
  padding-right: 100px;
}

.left-user-column-field{
  padding-right: 65px;
}

.right-user-column-field{
  padding-left: 65px;
}

.add-save-buttons{
  padding-right: 100px !important;
  gap: 20px;
}
