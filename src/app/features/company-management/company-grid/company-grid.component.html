<div class="company-container">
  <ng-container *ngIf="!showOnboarding">
    <div class="company-tabs-wrapper">
      <div class="company-stats-card">
        <nz-card data-testid="company-stats-card">
          <div class="stats-container">
            <div class="stat-item">
              <span class="stat-label">Total Company</span>
              <span class="stat-value" data-testid="total-companies-count">{{
                totalCompanies
              }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">New Company</span>
              <span class="stat-value" data-testid="new-companies-count">{{
                newCompanies
              }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Discontinued Company</span>
              <span
                class="stat-value"
                data-testid="discontinued-companies-count"
                >{{ discontinuedCompanies }}</span
              >
            </div>
          </div>
        </nz-card>
      </div>

      <div class="controls-section">
        <div class="checkbox-group">
          <nz-checkbox
            nz-checkbox
            [(nzChecked)]="filterActive"
            (nzCheckedChange)="onFilterActiveChange($event)"
            class="checkbox"
            data-testid="filter-active-checkbox"
          >
            Active
          </nz-checkbox>
          <nz-checkbox
            nz-checkbox
            [(nzChecked)]="filterInactive"
            (nzCheckedChange)="onFilterInactiveChange($event)"
            class="checkbox"
            data-testid="filter-inactive-checkbox"
          >
            Inactive
          </nz-checkbox>
          <nz-checkbox
            nz-checkbox
            [(nzChecked)]="filterAll"
            (nzCheckedChange)="onFilterAllChange($event)"
            class="checkbox"
            data-testid="filter-all-checkbox"
          >
            All
          </nz-checkbox>
        </div>
        <button
          nz-button
          class="add-company-button"
          type="button"
          (click)="showOnboardingForm()"
          data-testid="add-company-button"
        >
          Add Company
        </button>
      </div>
    </div>

    <app-data-grid
      class="company-table-datagrid"
      [tableColumns]="companyTableColumns"
      [tableData]="companyTableData"
      [loading]="isLoading"
      [showPagination]="false"
      [scrollY]="null"
      (tableDataClick)="onTableDataClick($event)"
      data-testid="company-table"
    ></app-data-grid>
  </ng-container>

  <ng-container *ngIf="showOnboarding">
    <app-company
      (companyClose)="closeOnboardingForm()"
      (companyCreated)="onCompanyCreated($event)"
    >
    </app-company>
  </ng-container>

  <ng-container *ngIf="openEditForm">
    <app-edit-company
      [isDrawerVisible]="openEditForm"
      [selectedCompany]="selectedCompany"
      (OnClose)="editDrawerClose()"
      (companyUpdated)="onCompanyUpdated($event)"
    ></app-edit-company>
  </ng-container>
</div>
