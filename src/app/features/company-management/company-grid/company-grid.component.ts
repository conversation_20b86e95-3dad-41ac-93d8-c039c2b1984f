import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { Router, ActivatedRoute } from '@angular/router';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzModalService } from 'ng-zorro-antd/modal';

import { Observable, of, Subject, Subscription } from 'rxjs';
import {
  debounceTime,
  distinctUntilChanged,
  retry,
  delay,
} from 'rxjs/operators';

import { StateDTO, AddressResponseDTO, CompanyDTO } from '../../../api-client';
import { COMMON_STRINGS } from '../../../core/constants/common';
import { ICompanyFields } from '../../../core/interface/company-fields';
import { ITableDataClickOutput } from '../../../core/interface/table';
import { NotificationService } from '../../../core/services/notification.service';
import { RegisterService } from '../../../core/services/register.service';
import { COMPANY_TABLE_COLUMNS } from '../../../core/tableColumns/company.column';
import { DataGridComponent } from '../../data-grid/data-grid.component';
import { CompanyComponent } from '../company/company.component';
import { EditCompanyComponent } from '../edit-company/edit-company.component';
import { CompanyState } from '../../../core/components/company-state';

@Component({
  selector: 'app-company-grid',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzCheckboxModule,
    NzButtonModule,
    DataGridComponent,
    CompanyComponent,
    EditCompanyComponent,
  ],
  templateUrl: './company-grid.component.html',
  styleUrls: ['./company-grid.component.scss'],
})
export class CompanyGridComponent implements OnInit, OnDestroy {
  isLoading = false;
  totalCompanies = 0;
  newCompanies = 0;
  discontinuedCompanies = 0;
  allCompanies: ICompanyFields[] = [];
  filterActive = false;
  filterInactive = false;
  filterAll = true;
  companyTableColumns = COMPANY_TABLE_COLUMNS;
  companyTableData: ICompanyFields[] = [];
  showOnboarding = false;
  private subscriptions: Subscription = new Subscription();
  openEditForm = false;
  selectedCompany: ICompanyFields | null = null;
  selectedCompanyId?: number | null = null;
  states$: Observable<StateDTO[]> = of([]);
  pageTitle = 'Company Management';

  private toggleInProgress = new Set<number>();
  private toggleSubject = new Subject<{
    companyId: number;
    currentStatus: boolean;
  }>();

  constructor(
    private registerService: RegisterService,
    private notification: NotificationService,
    private companyStateService: CompanyState,
    private router: Router,
    private route: ActivatedRoute,
    private modal: NzModalService,
  ) {}

  ngOnInit(): void {
    this.subscriptions.add(
      this.route.data.subscribe((data) => {
        this.pageTitle = data['title'] || 'Company Management';
        if (this.router.url.includes('add-company')) {
          this.showOnboarding = true;
        }
      }),
    );
    this.fetchCompanies();
    this.setupToggleDebounce();
  }

  private setupToggleDebounce(): void {
    this.subscriptions.add(
      this.toggleSubject
        .pipe(
          debounceTime(300),
          distinctUntilChanged(
            (prev, curr) =>
              prev.companyId === curr.companyId &&
              prev.currentStatus === curr.currentStatus,
          ),
        )
        .subscribe(({ companyId, currentStatus }) => {
          this.performToggle(companyId, currentStatus);
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    this.toggleInProgress.clear();
    this.toggleSubject.complete();
  }

  fetchCompanies(): void {
    this.isLoading = true;
    this.registerService.getAllCompanies().subscribe({
      next: (response) => {
        const companies = Array.isArray(response.data?.content)
          ? response.data.content
          : [];
        this.allCompanies = companies;
        this.updateStats();
        this.applyFilters();
        this.isLoading = false;
      },
      error: (error: HttpErrorResponse) => {
        this.notification.error(
          COMMON_STRINGS.errorMessages.failedToFetchCompanies,
        );
        console.error('Error fetching companies:', error);
        this.isLoading = false;
      },
    });
  }

  private updateStats(): void {
    this.newCompanies = this.allCompanies.filter((c) => c.isActive).length;
    this.discontinuedCompanies = this.allCompanies.filter(
      (c) => !c.isActive,
    ).length;
    this.totalCompanies = this.newCompanies + this.discontinuedCompanies;
  }

  applyFilters() {
    let filteredCompanies = [...this.allCompanies];
    const mappedCompanies: ICompanyFields[] = filteredCompanies.map(
      (company) => ({
        ...company,
        primaryAddressFormatted:
          this.formatAddress(company.primaryAddress) || 'N/A',
        name: company.name || 'N/A',
        accountsContactName: company.accountsContactName || 'N/A',
        billingEmail: company.billingEmail || 'N/A',
        activeUserCount: company.activeUserCount || 0,
        totalUsers: company.totalUsers || company.activeUserCount || 0,
        totalDocumentsOrdered: company.totalDocumentsOrdered || 0,
        totalDocumentPrice: company.totalDocumentPrice || 0,
        isActive: company.isActive || false,
      }),
    );
    if (this.filterAll || (!this.filterActive && !this.filterInactive)) {
      this.companyTableData = mappedCompanies;
    } else {
      filteredCompanies = filteredCompanies.filter((c) => {
        if (this.filterActive && c.isActive) return true;
        if (this.filterInactive && !c.isActive) return true;
        return false;
      });
      this.companyTableData = filteredCompanies.map((company) => ({
        ...company,
        primaryAddressFormatted:
          this.formatAddress(company.primaryAddress) || 'N/A',
        name: company.name || 'N/A',
        accountsContactName: company.accountsContactName || 'N/A',
        billingEmail: company.billingEmail || 'N/A',
        activeUserCount: company.activeUserCount || 0,
        totalUsers: company.totalUsers || company.activeUserCount || 0,
        totalDocumentsOrdered: company.totalDocumentsOrdered || 0,
        totalDocumentPrice: company.totalDocumentPrice || 0,
        isActive: company.isActive || false,
      }));
    }
  }

  private formatAddress(address?: AddressResponseDTO): string {
    if (!address) return 'N/A';
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.suburb,
      address.stateName,
      address.zipCode,
    ].filter((part) => part);
    return parts.join(', ') || 'N/A';
  }

  onFilterActiveChange(checked: boolean): void {
    this.filterActive = checked;
    if (checked) {
      this.filterAll = false;
      this.filterInactive = false;
    } else if (!this.filterInactive) {
      this.filterAll = true;
    }
    this.applyFilters();
  }

  onFilterInactiveChange(checked: boolean): void {
    this.filterInactive = checked;
    if (checked) {
      this.filterAll = false;
      this.filterActive = false;
    } else if (!this.filterActive) {
      this.filterAll = true;
    }
    this.applyFilters();
  }

  onFilterAllChange(checked: boolean): void {
    this.filterAll = checked;
    if (checked) {
      this.filterActive = false;
      this.filterInactive = false;
    }
    this.applyFilters();
  }

  onTableDataClick(data: ITableDataClickOutput<ICompanyFields>): void {
    const { actionField, rowData } = data;
    switch (actionField) {
      case 'view':
        this.viewCompany(rowData);
        break;
      case 'Edit':
        this.editDrawerOpen(rowData);
        break;
      case 'toggle':
        this.toggleCompanyStatus(rowData);
        break;
    }
  }

  toggleCompanyStatus(rowData: ICompanyFields): void {
    if (!rowData.id) {
      this.notification.error(COMMON_STRINGS.errorMessages.companyIdNotFound);
      return;
    }

    const currentCompany = this.allCompanies.find((c) => c.id === rowData.id);
    if (!currentCompany) {
      this.notification.error(
        COMMON_STRINGS.errorMessages.companyNotFoundInData,
      );
      return;
    }

    const currentStatus = currentCompany.isActive ?? false;
    const willDeactivate = currentStatus === true;
    const companyName = currentCompany.name || 'this company';
    const confirmationMessage = willDeactivate
      ? COMMON_STRINGS.confirmMessages.deactivateCompanyConfirmation.replace(
          '${companyName}',
          companyName,
        )
      : `Are you sure you want to activate ${companyName}?`;

    this.modal.confirm({
      nzTitle: confirmationMessage,
      nzOkText: willDeactivate
        ? COMMON_STRINGS.dialogConfigurations.buttonLabels.ConfirmDeactivate
        : COMMON_STRINGS.dialogConfigurations.buttonLabels.ConfirmActivate,
      nzOkType: 'primary',
      nzOkDanger: willDeactivate,
      nzClassName: 'custom-modal',
      nzCancelText: COMMON_STRINGS.dialogConfigurations.buttonLabels.Cancel,
      nzOnOk: () => {
        this.toggleSubject.next({
          companyId: rowData.id!,
          currentStatus: currentStatus,
        });
      },
    });
  }

  private performToggle(companyId: number, currentStatus: boolean): void {
    if (this.toggleInProgress.has(companyId)) {;
      return;
    }

    const newStatus = !currentStatus;
    const actionText = newStatus ? 'activating' : 'deactivating';

    this.toggleInProgress.add(companyId);
    this.registerService
      .activeInactiveCompany(companyId, newStatus)
      .pipe(
        retry({
          count: 2,
          delay: (_error, retryCount) => {
            return of(null).pipe(delay(1000 * retryCount));
          },
        }),
      )
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            const statusText = newStatus ? 'activated' : 'deactivated';
            this.notification.success(
              COMMON_STRINGS.successMessages.companyToggleSuccess.replace(
                '${statusText}',
                statusText,
              ),
            );
            this.fetchCompanies();
          } else {
            const errorMessage = response.message || 'Unknown error occurred';
            console.error('API response indicates failure:', response);

            if (
              errorMessage.includes('Keycloak') ||
              errorMessage.includes('user statuses')
            ) {
              this.notification.error(
                COMMON_STRINGS.errorMessages.failedToToggleCompanyKeycloak.replace(
                  '${actionText}',
                  actionText,
                ),
              );
            } else {
              this.notification.error(
                COMMON_STRINGS.errorMessages.failedToToggleCompany
                  .replace('${actionText}', actionText)
                  .replace('${errorMessage}', errorMessage),
              );
            }
          }

          this.toggleInProgress.delete(companyId);
        },
        error: (error) => {
          console.error('Error updating company status:', error);

          let errorMessage = 'Network error occurred';

          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          if (
            errorMessage.includes('Keycloak') ||
            errorMessage.includes('user statuses') ||
            errorMessage.includes('Failed to deactivate user')
          ) {
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToToggleCompanyUserPermissions.replace(
                '${actionText}',
                actionText,
              ),
            );
          } else if (errorMessage.includes('CompanyException')) {
            const cleanErrorMessage =
              errorMessage.split(':').pop()?.trim() ||
              COMMON_STRINGS.errorMessages.companyOperationFailed;
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToToggleCompanyException
                .replace('${actionText}', actionText)
                .replace('${errorMessage}', cleanErrorMessage),
            );
          } else {
            this.notification.error(
              COMMON_STRINGS.errorMessages.failedToToggleCompany
                .replace('${actionText}', actionText)
                .replace('${errorMessage}', errorMessage),
            );
          }

          this.toggleInProgress.delete(companyId);

          this.fetchCompanies();
        },
      });
  }

  viewCompany(rowData: ICompanyFields): void {
    if (rowData.id) {
      this.companyStateService.setCompanyData(rowData);
      this.router.navigate([rowData.id], { relativeTo: this.route });
    } else {
      this.notification.error(
        COMMON_STRINGS.warningMessages.failedToOpenCompanyView,
      );
    }
  }

  editDrawerOpen(rowData: ICompanyFields): void {
    this.selectedCompany = rowData;
    this.openEditForm = true;
  }

  showOnboardingForm(): void {
    this.router.navigate(['add-company'], { relativeTo: this.route });
  }

  closeOnboardingForm(): void {
    this.router.navigate([''], { relativeTo: this.route });
  }

  editDrawerClose(): void {
    this.openEditForm = false;
    this.selectedCompany = null;
  }

  onCompanyUpdated(updatedCompany: CompanyDTO): void {
    this.fetchCompanies();
    this.editDrawerClose();
    this.notification.success(
      COMMON_STRINGS.successMessages.companyUpdatedSuccess.replace(
        '${companyName}',
        updatedCompany.name || '',
      ),
    );
  }

  onCompanyCreated(newCompany: CompanyDTO): void {
    this.allCompanies = [newCompany, ...this.allCompanies];
    this.updateStats();
    this.applyFilters();
    this.closeOnboardingForm();
    this.notification.success(
      COMMON_STRINGS.successMessages.companyCreatedSuccess.replace(
        '${companyName}',
        newCompany.name || '',
      ),
    );
  }
}
