.company-form-wrapper {
  display: flex;
  justify-content: center;
}

.company-form {
  position: relative;
  width: 100%;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  background: #E8F3FE;
}

.preview-item {
  margin-bottom: 10px;
  padding: 5px 0;
}

.preview-item strong {
  color: #333;
  margin-right: 10px;
}

::ng-deep .ant-card-bordered {
  background: #ffffff;
  margin-bottom: 20px;
}

::ng-deep .ant-card-head {
  background-color: #f5f5f5;
  border-bottom: 1px solid #d9d9d9;
}

::ng-deep .ant-card-head-title {
  font-weight: bold;
  color: #333;
}

.nz-icon-style {
  cursor: pointer;
  color: var(--primary-button-color);
  font-size: 25px;
}

.nz-icon-style:hover {
  color: #0056b3;
}

.user-grid {
  width: 100%;
  margin-top: 15px;
  font-size: 14px;
  color: #333;
}

.user-grid-header,
.user-grid-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr; 
  gap: 16px;
  padding: 12px 0;
  align-items: center;
}

.user-grid-header {
  font-weight: bold;
  color: #666;
  display: flex;
  justify-content: space-between;
  padding-right: 25rem;
}

.user-grid-row {
  padding: 12px 16px;
  transition: background-color 0.2s ease;
}


.user-grid-row:last-child {
  border-bottom: none;
}

.user-grid-cell {
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

@media (max-width: 768px) {
  .user-grid-header,
  .user-grid-row {
    grid-template-columns: 1fr 1fr; 
    gap: 12px;
  }

  .user-grid-cell:nth-child(3) {
    grid-column: 1 / -1; 
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e8e8e8;
  }
}

@media (max-width: 480px) {
  .user-grid-header,
  .user-grid-row {
    grid-template-columns: 1fr; 
    gap: 8px;
  }

  .user-grid-cell:nth-child(3) {
    grid-column: auto;
    margin-top: 4px;
    padding-top: 4px;
  }
}

.user-preview-table {
  width: 100%;
  margin-top: 10px;
  border-collapse: collapse;
}

.user-preview-table th,
.user-preview-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e8e8e8;
  vertical-align: middle;
}

.user-preview-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  color: #666;
  border-bottom: 2px solid #d9d9d9;
}

.user-preview-table tr:hover {
  background-color: #f9f9f9;
}

.create-company-button {
  background-color: var(--primary-button-color);
  color: white;
  height: 40px;
  border: none;
  border-radius: 5px;
  padding: 0 20px;
  font-weight: bold;
}

.create-company-button:hover {
  background-color: #0056b3;
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

.create-company-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  cursor: not-allowed;
  transform: none;
}

.navigation-buttons {
  display: flex;
  z-index: 1000;
  margin: 2rem 6rem;
}

.next-button {
  bottom: 75px;
  right: 4%;
  min-width: 100px;
  background-color: var(--primary-button-color);
  border: none;
  color: #ffffff;
  border-radius: 5px;
  height: 40px;
  z-index: 1000;
  cursor: pointer;
}

.next-button:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

.next-button:disabled {
  background-color: #d3d3d3;
  color: #a9a9a9;
  border-color: #d3d3d3;
  cursor: not-allowed;
}

.card-title{
  font-weight: bold;
}
