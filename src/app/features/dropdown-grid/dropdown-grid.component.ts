import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzTableModule } from 'ng-zorro-antd/table';
import { TransactionDTO } from '../../api-client';
import { TransactionService } from '../../core/services/transaction.service';
import { EnumTransactionTypes } from '../../core/enumerations/transaction-types';

@Component({
  selector: 'app-dropdown-grid',
  standalone: true,
  imports: [CommonModule, NzCollapseModule, NzTableModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './dropdown-grid.component.html',
  styleUrls: ['./dropdown-grid.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class DropdownGridComponent implements OnInit {
  transactionData: TransactionDTO[] = [];
  private transactionService = inject(TransactionService);

  @Input() isExpanded = false;
  @Input() visibleInstrumentDrawer = false;
  @Input() hideArrow = false;
  @Output() visibleInstrumentDrawerChange = new EventEmitter<boolean>();
  @Output() isExpandedChange = new EventEmitter<boolean>();

  ngOnInit(): void {
    this.loadRechargeTransactions();
  }

  loadRechargeTransactions(): void {
    this.transactionService.getAllTransactions().subscribe({
      next: (transactions) => {
        this.transactionData = transactions.filter(
          (txn) => txn.transactionType === EnumTransactionTypes.RECHARGE,
        );
      },
    });
  }

  onCollapseChange(event: unknown): void {
    const activePanels = event as number[];
    this.isExpanded = Array.isArray(activePanels) && activePanels.includes(0);
    this.isExpandedChange.emit(this.isExpanded);
  }

  toggleCollapse(): void {
    this.isExpanded = !this.isExpanded;
    this.isExpandedChange.emit(this.isExpanded);
  }
}
