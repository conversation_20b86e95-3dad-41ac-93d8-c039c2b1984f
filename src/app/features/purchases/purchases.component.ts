import { Component, EventEmitter, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

// NG-ZORRO Modules
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzInputNumberModule } from 'ng-zorro-antd/input-number';
import { NzListModule } from 'ng-zorro-antd/list';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

// Feature Components
import { TransactionsComponent } from './transactions/transactions.component';
import { WalletComponent } from '../../core/features/wallet/wallet.component';

// Services
import { TransactionService } from '../../core/services/transaction.service';
import { RechargeService } from '../../core/services/recharge.service';
import { NotificationService } from '../../core/services/notification.service';
import { UserService } from '../../core/services/user.service';

// Constants
import { ROUTES } from '../../core/constants/routes';
import {
  COMMON_STRINGS,
  DEFAULT_RECHARGE_AMOUNT,
  PRESET_AMOUNT,
} from '../../core/constants/common';

// DTOs & Enums
import {
  OrderItemDTO,
  TransactionDTO as BaseTransactionDTO,
} from '../../api-client';
import { EnumTransactionTypes } from '../../core/enumerations/transaction-types';
import { EnumUserRole } from '../../core/enumerations/user-roles';

import { DataGridComponent } from '../data-grid/data-grid.component';
import { WALLET_HISTORY_TABLE_COLUMNS } from '../../core/tableColumns/walletHistory.column';

interface TransactionDTO extends BaseTransactionDTO {
  expanded?: boolean;
}

@Component({
  selector: 'app-purchases',
  standalone: true,
  templateUrl: './purchases.component.html',
  styleUrls: ['./purchases.component.scss'],
  imports: [
    CommonModule,
    FormsModule,
    NzButtonModule,
    NzCardModule,
    NzCollapseModule,
    NzDrawerModule,
    NzIconModule,
    NzInputModule,
    NzInputNumberModule,
    NzListModule,
    NzModalModule,
    NzSkeletonModule,
    NzDatePickerModule,
    TransactionsComponent,
    WalletComponent,
    DataGridComponent,
  ],
})
export class PurchasesComponent implements OnInit {
  @Input() transactionId?: number;
  @Input() isExpanded = false;
  @Input() isExpandedChange = new EventEmitter<boolean>();
  dropdownExpanded = false;

  walletBalance = 0;
  transactionData: TransactionDTO[] = [];
  allTransactions: TransactionDTO[] = [];
  rechargeTransactions: TransactionDTO[] = [];
  purchaseTransactions: TransactionDTO[] = [];
  originalTransactions: TransactionDTO[] = [];
  visibleTransactions: TransactionDTO[] = [];
  orderData: OrderItemDTO[] = [];
  WALLET_HISTORY_TABLE_COLUMNS = WALLET_HISTORY_TABLE_COLUMNS;

  isLoading = false;
  drawerVisible = false;
  rechargeModalVisible = false;
  showWalletHistory = false;
  walletTitle = 'Wallet History';

  searchText = '';
  searchUsers: string[] = [];
  amount: number = DEFAULT_RECHARGE_AMOUNT;
  presetAmounts = PRESET_AMOUNT;

  selectedDate: Date | null = null;
  selectedDateRange: [Date, Date] | null = null;

  readonly enumUserRole = EnumUserRole;

  private userId?: number;

  // View More logic
  visibleCount = 7;
  showAll = false;

  constructor(
    private transactionService: TransactionService,
    private rechargeService: RechargeService,
    private notificationService: NotificationService,
    private userService: UserService,
    private route: ActivatedRoute,
    private router: Router,
  ) {}

  async ngOnInit(): Promise<void> {
    await this.setUser();
    this.loadWalletBalance();
    this.loadTransactions();
    this.loadTransactionsRecharge();
  }

  private async setUser(): Promise<void> {
    try {
      const userInfo = await this.userService.getUserRole();
      this.userId = userInfo?.id;
      if (!this.userId) {
        this.showError(COMMON_STRINGS.errorMessages.handleUserError);
      }
    } catch (error) {
      console.error('Error getting user:', error);
      this.showError(COMMON_STRINGS.errorMessages.handleUserError);
    }
  }

  loadTransactions(): void {
    this.isLoading = true;
    this.transactionService.getAllTransactions().subscribe({
      next: (res) => {
        // Filter transactions where the transaction type is PURCHASE
        const purchaseTransactions = res.filter(
          (txn) => txn.transactionType === EnumTransactionTypes.PURCHASE,
        );

        this.transactionData = purchaseTransactions;
        this.allTransactions = purchaseTransactions;
        this.originalTransactions = [...purchaseTransactions];
        this.visibleTransactions = purchaseTransactions.slice(
          0,
          this.visibleCount,
        );
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Transactions error:', err);
        this.showError(COMMON_STRINGS.errorMessages.failedToFetchTransactions);
      },
    });
  }

  loadTransactionsRecharge(): void {
    this.isLoading = true;
    this.transactionService.getAllTransactions().subscribe({
      next: (res) => {
        // Filter transactions where the transaction type is RECHARGE
        const rechargeTransactions = res.filter(
          (txn) => txn.transactionType === EnumTransactionTypes.RECHARGE,
        );
        this.rechargeTransactions = rechargeTransactions;
        this.isLoading = false;
      },
      error: (err) => {
        console.error('Transactions error:', err);
        this.showError(COMMON_STRINGS.errorMessages.failedToFetchTransactions);
      },
    });
  }

  private loadWalletBalance(): void {
    if (!this.userId) return;
    this.rechargeService.getUserWalletBalance(this.userId).subscribe({
      next: (balance) => (this.walletBalance = balance),
      error: (err) => {
        console.error('Wallet balance error:', err);
        this.showError(COMMON_STRINGS.errorMessages.failedToFetchWalletBalance);
      },
    });
  }

  rechargeWallet(): void {
    if (!this.userId) {
      this.showError(COMMON_STRINGS.errorMessages.handleUserError);
      return;
    }

    const loadingId = this.notificationService.showLoaderMessage(
      COMMON_STRINGS.loadingMessage.rechargeWallet,
    );

    this.rechargeService.rechargeWallet(this.userId, this.amount).subscribe({
      next: (res) => {
        this.notificationService.hideLoaderMessage(loadingId);
        this.walletBalance = res.newBalance ?? this.walletBalance;

        const currentRoute = this.router.url;
        this.router.navigate([ROUTES.mockRecharge], {
          queryParams: { paymentAmount: this.amount },
        });

        setTimeout(() => {
          this.router.navigate([currentRoute]);
        }, 10000);

        this.drawerVisible = false;
      },
      error: (err) => {
        console.error('Recharge error:', err);
        this.notificationService.hideLoaderMessage(loadingId);
        this.showError(COMMON_STRINGS.errorMessages.failedToRechargeWallet);
      },
    });
  }

  onDrawerCloseAfterDelay(delay: number): void {
    setTimeout(() => (this.drawerVisible = false), delay);
  }

  searchTransaction(): void {
    const searchValue = this.searchText.trim().toLowerCase();
    const filtered = searchValue
      ? this.originalTransactions.filter((txn) =>
          txn.transactionId?.toString().toLowerCase().includes(searchValue),
        )
      : [...this.originalTransactions];

    this.transactionData = filtered;
    this.visibleTransactions = filtered.slice(0, this.visibleCount);
    this.showAll = filtered.length <= this.visibleCount;
  }

  clearDateFilter(): void {
    this.selectedDateRange = null;
    this.transactionData = [...this.originalTransactions];
    this.visibleTransactions = this.transactionData.slice(0, this.visibleCount);
    this.showAll = this.transactionData.length <= this.visibleCount;
  }
  onDateChange(dates: [Date, Date] | null): void {
    if (!dates || !Array.isArray(dates) || dates.length !== 2) {
      this.clearDateFilter();
      return;
    }

    const [startDate, endDate] = dates;
    const startOfDay = new Date(startDate).setHours(0, 0, 0, 0);
    const endOfDay = new Date(endDate).setHours(23, 59, 59, 999);

    const filtered = this.originalTransactions.filter((txn) => {
      const txnDate = new Date(txn.transactionDate ?? 0).getTime();
      return txnDate >= startOfDay && txnDate <= endOfDay;
    });

    this.transactionData = filtered;
    this.visibleTransactions = filtered.slice(0, this.visibleCount);
    this.showAll = filtered.length <= this.visibleCount;
  }

  toggleExpand(transaction: TransactionDTO): void {
    transaction.expanded = !transaction.expanded;
  }

  setPresetAmount(value: number): void {
    this.amount = value;
  }

  closeWalletDrawer(): void {
    this.drawerVisible = false;
  }
  closeWalletHistoryDrawer(): void {
    this.showWalletHistory = false;
    this.dropdownExpanded = false; // Reset when closing
  }

  loadMore(): void {
    const next = this.visibleTransactions.length + this.visibleCount;
    this.visibleTransactions = this.transactionData.slice(0, next);
    this.showAll =
      this.visibleTransactions.length >= this.transactionData.length;
  }

  getCurrentUser(): string {
    return this.userService.userInfo?.firstName ?? 'John Doe';
  }

  getCurrentUserRole(): number {
    return this.userService.userInfo?.roleId ?? 0;
  }

  get transactions(): TransactionDTO[] {
    return this.isLoading
      ? Array(this.visibleCount).fill({} as TransactionDTO)
      : this.visibleTransactions;
  }

  private showError(message: string): void {
    this.notificationService.error(message);
    this.isLoading = false;
  }

  openWalletHistory(): void {
    this.showWalletHistory = true;
  }
}
