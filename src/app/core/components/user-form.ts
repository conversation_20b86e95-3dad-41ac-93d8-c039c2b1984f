import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import {
  UserRequestDTO,
  AddressRequestDTO,
  RoleDTO,
  UserResponseDTO,
} from '../../api-client';
import { IUserFields } from '../interface/user-fields';
import { ICompanyFields } from '../interface/company-fields';

@Injectable({
  providedIn: 'root',
})
export class UserRequestMapper {
  createUserRequest(
    form: FormGroup,
    company: ICompanyFields | null | undefined = null,
    companyId: number | undefined,
  ): UserRequestDTO | null {
    if (
      !company ||
      !company.primaryAddress ||
      !company.billingAddress ||
      !companyId
    ) {
      return null;
    }

    const primaryAddress: AddressRequestDTO = {
      addressLine1: company.primaryAddress.addressLine1 || '',
      addressLine2: company.primaryAddress.addressLine2 || '',
      suburb: company.primaryAddress.suburb || '',
      stateId: company.primaryAddress.stateId || 0,
      zipCodeId: company.primaryAddress.zipCodeId || 0,
    };

    const billingAddress: AddressRequestDTO = {
      addressLine1: company.billingAddress.addressLine1 || '',
      addressLine2: company.billingAddress.addressLine2 || '',
      suburb: company.billingAddress.suburb || '',
      stateId: company.billingAddress.stateId || 0,
      zipCodeId: company.billingAddress.zipCodeId || 0,
    };

    const userRequest: UserRequestDTO = {
      firstName: form.value.firstName,
      lastName: form.value.lastName,
      email: form.value.email,
      contactNumber: form.value.contactNumber,
      roleId: form.value.roleId,
      profilePictureUrl: form.value.profilePictureUrl,
      userType: UserRequestDTO.UserTypeEnum.Company,
      primaryAddress: primaryAddress,
      billingAddress: billingAddress,
      companyId: companyId,
    };

    return userRequest;
  }

  mapUserResponseToUserFields(
    userData: UserResponseDTO | null,
    roles: RoleDTO[],
    company: ICompanyFields | null | undefined = null,
  ): IUserFields | null {
    if (!userData) {
      return null;
    }

    const roleName = this.getRoleName(userData.roleId || 0, roles);
    const updatedUser: IUserFields = {
      id: userData.id,
      firstName: userData.firstName,
      lastName: userData.lastName,
      email: userData.email,
      contactNumber: userData.contactNumber,
      roleId: userData.roleId,
      profilePictureUrl: userData.profilePictureUrl,
      isActive: userData.isActive,
      fullName: `${userData.firstName} ${userData.lastName}`,
      roleDisplayText: roleName,
      primaryAddress: company?.primaryAddress,
      billingAddress: company?.billingAddress,
    };

    return updatedUser;
  }

  private getRoleName(roleId: number, roles: RoleDTO[]): string {
    const role = roles.find((r) => r.id === roleId);
    return role?.displayText || 'N/A';
  }
}
