import {
  NUM_SORT_FN,
  STRING_CASE_INSENSITIVE_SORT_FN,
} from '../constants/functions';
import { ICompanyFields } from '../interface/company-fields';
import { ITableColumn } from '../interface/table';

export const COMPANY_TABLE_COLUMNS: ITableColumn<ICompanyFields>[] = [
  {
    header: 'Company Name',
    field: 'name',
    fieldType: 'textWithCaption',
    width: '30rem',
    sortFn: (a, b) => STRING_CASE_INSENSITIVE_SORT_FN(a.name, b.name),
    getText: (rowData: ICompanyFields) => rowData.name || 'N/A',
    getCaption: (rowData: ICompanyFields) =>
      rowData.primaryAddressFormatted || 'N/A',
  },
  {
    header: 'Key AC holder',
    field: 'accountsContactName',
    fieldType: 'textWithCaption',
    width: '20rem',
    getText: (rowData: ICompanyFields) => rowData.accountsContactName || 'N/A',
    getCaption: (rowData: ICompanyFields) => rowData.billingEmail || 'N/A',
  },
  {
    header: 'Users',
    field: 'totalUsers',
    fieldType: 'text',
    width: '10rem',
    sortFn: (a, b) =>
      NUM_SORT_FN(
        a.totalUsers || a.activeUserCount || 0,
        b.totalUsers || b.activeUserCount || 0,
      ),
  },
  {
    header: 'Purchased Docs',
    field: 'totalDocumentsOrdered',
    fieldType: 'number',
    width: '10rem',
    sortFn: (a, b) =>
      NUM_SORT_FN(a.totalDocumentsOrdered, b.totalDocumentsOrdered),
  },
  {
    header: 'Total Billing',
    field: 'totalDocumentPrice',
    fieldType: 'number',
    width: '10rem',
    sortFn: (a, b) => NUM_SORT_FN(a.totalDocumentPrice, b.totalDocumentPrice),
  },
  {
    header: 'Action',
    field: 'action',
    fieldType: 'actionFields',
    width: '10rem',
    actionFieldColumns: [
      {
        field: 'view',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'eye',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'View company',
      },
      {
        field: 'Edit',
        header: '',
        fieldType: 'icon',
        iconType: 'nzIcon',
        nzIconType: 'edit',
        nzIconTheme: 'outline',
        showTooltip: true,
        iconTooltipTitle: 'Edit company',
        conditionForShowActionField: (rowData: ICompanyFields) =>
          rowData.isActive === true,
      },
      {
        field: 'toggle',
        header: '',
        fieldType: 'toggle',
      },
    ],
  },
];
